    import styles from './style.module.scss';
    import React, { useRef } from 'react';
    import Rounded from '../../common/RoundedButton';
    import { useLenisParallax } from '@/hooks/useLenisParallax';
    import GSAPTextReveal from '@/components/GSAPTextReveal';
    import GSAPParagraphs from '@/components/GSAPTextReveal/GSAPParagraphs';
    import { getPreset } from '@/components/GSAPTextReveal/presets';

    export default function Description({
        descriptionTitle = "Helping brands to stand out in the digital era. Together we will set the new status quo. No nonsense, always on the cutting edge.",
        descriptionText = "The combination of my passion for design, code & interaction positions me in a unique place in the web design world.",
        showButton = true,
        buttonText = "About me",
        titleTag = "h2" // Nouveau prop pour spécifier le niveau de titre
    }) {

        const description = useRef(null);
        const parallaxRef = useLenisParallax(0.1);

        return (
            <div ref={description} className={`${styles.description} container`}>
                <div className={styles.body}>
                                          <GSAPTextReveal
                  as={titleTag}
                  className={styles.title}
                  {...getPreset('hero')}
                >
                  {descriptionTitle}
                </GSAPTextReveal>

                    <div className={styles.descriptionText}>
                        {typeof descriptionText === 'string' ? (
                            <GSAPTextReveal
                                as="p"
                                className="text-big"
                                {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
                            >
                                {descriptionText}
                            </GSAPTextReveal>
                        ) : Array.isArray(descriptionText) ? (
                            <div>
                                {descriptionText.map((paragraph, index) => (
                                    <GSAPTextReveal
                                        key={index}
                                        as="p"
                                        className="text-big"
                                        {...getPreset('lines', { delay: 0.8 + (index * 0.3), stagger: 0.2 })}
                                    >
                                        {paragraph}
                                    </GSAPTextReveal>
                                ))}
                            </div>
                        ) : React.isValidElement(descriptionText) ? (
                            // Si c'est déjà un élément JSX valide (comme <p>...</p>), on le rend directement
                            <GSAPTextReveal
                                as="div"
                                className="text-big"
                                {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
                            >
                                {descriptionText}
                            </GSAPTextReveal>
                        ) : (
                            <GSAPParagraphs
                                className="text-big"
                                preset="lines"
                                presetOverrides={{ delay: 0.8, stagger: 0.2 }}
                                paragraphSpacing={1.5}
                            >
                                {descriptionText}
                            </GSAPParagraphs>
                        )}
                    </div>
                    {showButton && (
                        <div ref={parallaxRef}>
                            <Rounded className={styles.button}>
                                <p>{buttonText}</p>
                            </Rounded>
                        </div>
                    )}
                </div>
            </div>
        );
    }
